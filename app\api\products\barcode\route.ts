import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

// 通过条形码或追溯码查询药品
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');

    if (!code) {
      return NextResponse.json(
        { success: false, message: '条码不能为空' },
        { status: 400 }
      );
    }

    // 查询药品 - 同时匹配条形码和追溯码
    const 药品信息 = await query(
      `SELECT
        p.编号 as id,
        p.名称 as name,
        p.通用名 as generic_name,
        p.描述 as description,
        p.药品标识码 as drug_identification_code,
        p.追溯码 as trace_code,
        p.分类编号 as category_id,
        c.名称 as category_name,
        p.生产厂家 as manufacturer,
        p.批准文号 as approval_number,
        p.规格 as specification,
        p.剂型 as dosage_form,
        p.售价 as price,
        p.成本价 as cost_price,
        p.库存数量 as stock_quantity,
        p.最低库存 as min_stock_level,
        p.是否处方药 as is_prescription,
        p.是否医保 as is_medical_insurance,
        p.储存条件 as storage_condition,
        p.状态 as status
      FROM 药品信息 p
      LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
      WHERE p.药品标识码 = ?
      LIMIT 1`,
      [code, code]
    );

    if (!药品信息 || 药品信息.length === 0) {
      return NextResponse.json(
        { success: false, message: '未找到匹配的药品' },
        { status: 404 }
      );
    }

    // 处理布尔值转换
    const processedProduct = {
      ...药品信息[0],
      is_prescription: 药品信息[0].is_prescription === 1,
      is_medical_insurance: 药品信息[0].is_medical_insurance === 1
    };

    return NextResponse.json({
      success: true,
      data: processedProduct
    });
  } catch (error) {
    console.error('通过条码查询药品失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '通过条码查询药品失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
